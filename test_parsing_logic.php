<?php

// Test de la logique de parsing pour maritime vs transport_routier
echo "Test de la logique de parsing...\n\n";

$urls = [
  'maritime' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx',
  'transport_routier' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=4356',
];

function fetchHtml($url) {
  $ch = curl_init();
  curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_USERAGENT => 'Drupal Scraper Bot',
  ]);
  
  $html = curl_exec($ch);
  curl_close($ch);
  return $html;
}

foreach ($urls as $type => $url) {
  echo "=== Test $type ===\n";
  echo "URL: $url\n";
  
  $html = fetchHtml($url);
  if ($html) {
    echo "HTML récupéré: " . strlen($html) . " caractères\n";
    
    // Test de la logique de détection
    $h1_count = substr_count($html, '<h1');
    $isList = $h1_count === 0;
    
    echo "Nombre de H1: $h1_count\n";
    echo "Détecté comme: " . ($isList ? 'Page de liste' : 'Article individuel') . "\n";
    
    // Test de parsing basique
    $dom = new \DOMDocument();
    @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR);
    $xpath = new \DOMXPath($dom);
    
    if ($isList) {
      $articles = $xpath->query('//div[@id="ctl00_SPWebPartManager1_g_1b059c32_8d39_44a2_aea7_47c3d33fd6ad_ctl00_detailPnl"] | //div[@id="ctl00_SPWebPartManager1_g_562ad89e_a8c9_4be8_96ce_f25d5c6214f4_ctl00_detailPnl"]');
    } else {
      $articles = $xpath->query('//div[@id="ctl00_SPWebPartManager1_g_1b059c32_8d39_44a2_aea7_47c3d33fd6ad_ctl00_detailPnl"]');
    }
    
    echo "Articles trouvés: " . $articles->length . "\n";
    
    if ($articles->length > 0) {
      $article = $articles->item(0);
      $content_length = strlen(trim($article->textContent));
      echo "Longueur du contenu: $content_length caractères\n";
      
      if ($content_length > 100) {
        echo "✓ Contenu suffisant trouvé\n";
      } else {
        echo "⚠️  Contenu insuffisant\n";
      }
    } else {
      echo "✗ Aucun article trouvé\n";
    }
    
  } else {
    echo "✗ Échec de récupération HTML\n";
  }
  
  echo "\n" . str_repeat("-", 50) . "\n\n";
}
